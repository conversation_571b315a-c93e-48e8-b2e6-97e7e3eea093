{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "AxonCoreShared": {"ApiHost": "http://localhost-not-used", "GrpcHost": "http://localhost-not-used", "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77", "ClientId": "6cc7e19d-3d23-455b-a2a3-f2a2e8af6d7c", "AppScope": "api://smartphlex-dev/.default", "AppName": "hacomms", "DataProtection": {"Container": "dataprotection", "Blob": "axon-dataprotection-key-ring"}}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "organizations", "ClientId": "167cd45b-7d4f-4b3d-8c05-a87f12c40609", "Scope": "api://smartphlex-dev/.default"}, "TenantSettings": {"Tenants": [{"Identifier": "phlexglobal"}, {"Identifier": "pharmalex"}]}, "AzureSearch": {"IsEnabled": false, "ServiceName": "ss-nonprod-ss-eun", "IndexName": "hacomms-index-dev", "IndexerName": "hacomms-indexer-dev", "DataSourceName": "hacomms-db-dev", "TableOrView": "SearchInComments", "Interval": "5", "HighWatermarkColumn": "LastUpdatedDate", "SoftDeleteColumn": "IsDeleted", "SoftDeleteMarker": "true"}, "ConnectionStrings": {"default": "Server=(local);Initial Catalog=hacomms-dev;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Connection Timeout=30;Integrated Security=True", "SendGrid": "test"}, "SoftDeleteHostedService": {"receivers": "<EMAIL>;<EMAIL>;mila.pandu<PERSON><PERSON>@pharmalex.com;<EMAIL>;<EMAIL>;", "sender": "<EMAIL>"}, "Swagger": {"BasePath": "/axon-hacomms-api"}, "Audit": {"SqlServer": {"ConnectionString": "Server=.;Initial Catalog=hacomms-dev;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Connection Timeout=30;Integrated Security=True", "Schema": "dbo", "TableName": "Audit", "IdColumnName": "Id", "JsonColumnName": "JsonData", "LastUpdatedDateColumnName": "LastUpdatedDate"}}, "NLog": {"throwConfigExceptions": true, "autoReload": true, "extensions": [{"assembly": "NLog.Web.AspNetCore"}], "targets": {"async": true, "filelog-default": {"type": "File", "fileName": "/app/logs/api-${shortdate}.log", "layout": "${level:padding=-6} | ${message:padding=-150}| ${logger} | ${longdate}${onexception:${newline}EXCEPTION\\: ${exception:format=message,stacktrace}}"}, "console-default": {"type": "<PERSON><PERSON><PERSON>", "layout": "${level:padding=-6} | ${message:padding=-150}| ${logger} | ${time}${onexception:${newline}EXCEPTION\\: ${exception:format=message,stacktrace}}"}}, "rules": [{"logger": "*", "minLevel": "Info", "writeTo": "console-default"}, {"logger": "*", "minLevel": "Debug", "writeTo": "filelog-default"}, {"logger": "Microsoft.*", "minlevel": "Info", "final": true}]}}