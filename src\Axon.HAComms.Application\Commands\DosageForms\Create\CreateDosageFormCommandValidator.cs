﻿using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.DosageForms.Create;

[UsedImplicitly]
public class CreateDosageFormCommandValidator : AbstractValidator<CreateDosageFormCommandRequest>
{
    public CreateDosageFormCommandValidator(IDosageFormsRepository repoDosageForm)
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoDosageForm.ExistsAsync(x => string.Equals(x.Name, request.Name)).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Dosage form with name '{request.Name}' already exists.");
            }
        });
    }
}
