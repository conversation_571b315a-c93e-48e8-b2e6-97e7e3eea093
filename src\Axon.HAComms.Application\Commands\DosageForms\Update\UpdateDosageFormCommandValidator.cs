using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.DosageForms.Update;

[UsedImplicitly]
public class UpdateDosageFormCommandValidator : AbstractValidator<UpdateDosageFormCommandRequest>
{
    public UpdateDosageFormCommandValidator(IDosageFormsRepository repoDosageForms)
    {
        RuleFor(x => x.Id)
            .NotEmpty();

        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoDosageForms.ExistsAsync(x => x.Id != request.Id && (string.Equals(x.Name, request.Name))).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Dosage form with name '{request.Name}' already exists.");
            }
        });
    }
}
