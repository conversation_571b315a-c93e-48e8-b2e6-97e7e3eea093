﻿using MediatR;
using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Commands.DrugSubstances.Create
{
    public class CreateDrugSubstanceCommandRequest : IRequest<CreateDrugSubstanceCommandResponse>
    {
        [MaxLength(100)]
        public string Name { get; }

        [Required]
        [MaxLength(50)]
        public string Code { get; }

        [MaxLength(500)]
        public string Description { get; }

        public CreateDrugSubstanceCommandRequest(string name, string code, string description)
        {
            this.Name = name;
            this.Code = code;
            this.Description= description;
        }
    }
}
