﻿using MediatR;
using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Commands.DrugSubstances.Update
{
    public class UpdateDrugSubstanceCommandRequest : IRequest<UpdateDrugSubstanceCommandResponse>
    {
        public int Id { get; }

        [MaxLength(100)]
        public string Name { get; }

        [Required]
        [MaxLength(50)]
        public string Code { get; }

        [MaxLength(500)]
        public string Description { get; }

        public UpdateDrugSubstanceCommandRequest(int id, string name, string code, string description)
        {
            Id = id;
            Name = name;
            Code = code;
            Description = description;
        }
    }
}
