using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.DrugSubstances.Update;

[UsedImplicitly]
public class UpdateDrugSubstanceCommandValidator : AbstractValidator<UpdateDrugSubstanceCommandRequest>
{
    public UpdateDrugSubstanceCommandValidator(IDrugSubstancesRepository repoDrugSubstances)
    {
        RuleFor(x => x.Id)
            .NotEmpty();

        RuleFor(x => x.Code)
            .NotEmpty()
            .MaximumLength(50)
            .WithMessage("Code cannot exceed 50 characters.");

        RuleFor(x => x.Name)
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters.");

        RuleFor(x => x)
            .Must(request =>
            {
                var exists = repoDrugSubstances.ExistsAsync(x => x.Id != request.Id && string.Equals(x.Code, request.Code));
                return !exists.GetAwaiter().GetResult();
            })
            .WithName("CodeAlreadyExists")
            .WithMessage(x => $"Code = ({x.Code}) already exists.");

        RuleFor(x => x)
            .Must(request =>
            {
                var exists = repoDrugSubstances.ExistsAsync(x => x.Id != request.Id && x.Name != Constants.NotAssigned && string.Equals(x.Name, request.Name));
                return !exists.GetAwaiter().GetResult();
            })
            .WithName("NameAlreadyExists")
            .WithMessage(x => $"Name = ({x.Name}) already exists.");
    }
}
