﻿using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;

[UsedImplicitly]
public class CreateRouteOfAdministrationCommandValidator : AbstractValidator<CreateRouteOfAdministrationCommandRequest>
{
    public CreateRouteOfAdministrationCommandValidator(IRouteOfAdministrationRepository repoRoutesOfAdministration)
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoRoutesOfAdministration.ExistsAsync(x => string.Equals(x.Name, request.Name)).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Route of administration with name '{request.Name}' already exists.");
            }
        });
    }
}
