﻿using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Tags.Create;

[UsedImplicitly]
public class CreateTagCommandValidator : AbstractValidator<CreateTagCommandRequest>
{
    public CreateTagCommandValidator(ITagRepository repoTags)
    {
        RuleFor(x => x.Name).NotEmpty();

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoTags.ExistsAsync(x => string.Equals(x.Name, request.Name)).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Tag with name '{request.Name}' already exists.");
            }
        });
    }
}
