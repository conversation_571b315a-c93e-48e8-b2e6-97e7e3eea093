using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Tags.Update;

[UsedImplicitly]
public class UpdateTagCommandValidator : AbstractValidator<UpdateTagCommandRequest>
{
    public UpdateTagCommandValidator(ITagRepository repoTags)
    {
        RuleFor(x => x.Id)
            .NotEmpty();

        RuleFor(x => x.Name)
            .NotEmpty();

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoTags.ExistsAsync(x => x.Id != request.Id && (string.Equals(x.Name, request.Name))).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Tag with name '{request.Name}' already exists.");
            }
        });
    }
}
