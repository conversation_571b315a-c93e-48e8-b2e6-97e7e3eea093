﻿using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Models.DrugSubstances
{
    public class DrugSubstanceModel
    {
        public int Id { get; set; }

        [MaxLength(100)]
        public string? Name { get; set; }

        [Required]
        [MaxLength(50)]
        public string Code { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        public bool IsAssociatedToComment { get; set; }
        public DrugSubstanceModel(int id, string code, string? name, string? description)
        {
            Id = id;
            Name = name;
            Code = code;
            Description = description;
        }
    }
}
