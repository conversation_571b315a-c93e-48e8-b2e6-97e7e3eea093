﻿using Axon.HAComms.Domain.Entities.Base;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Domain.Entities;

public class DrugSubstance : MultiTenantEntity
{
    public DrugSubstance()
    {

    }

    internal DrugSubstance(int id)
    {
        this.Id = id;
    }

    [MaxLength(100)]
    public string? Name { get; set; }

    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    public int? ExternalId { get; set; }

    public ICollection<Product> Products { get; set; } = [];
    public ICollection<DrugSubstanceDrugProduct> DrugSubstanceProducts { get; set; } = [];
    public ICollection<Comment> Comments { get; set; } = new HashSet<Comment>();
}
