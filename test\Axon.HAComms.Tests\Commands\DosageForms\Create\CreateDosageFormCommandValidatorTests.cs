﻿using Axon.HAComms.Application.Commands.DosageForms.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DosageForms.Create;

public class CreateDosageFormCommandValidatorTests
{
    private readonly CreateDosageFormCommandValidator sut;
    private readonly IDosageFormsRepository dosageFormRepository;

    public CreateDosageFormCommandValidatorTests()
    {
        this.dosageFormRepository = Substitute.For<IDosageFormsRepository>();
        sut = new CreateDosageFormCommandValidator(this.dosageFormRepository);
    }

    [Fact]
    public void Validate_NameIsEmpty_DoesThrowException()
    {
        var request = new CreateDosageFormCommandRequest(string.Empty);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("'Name' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameIsNotEmpty_DoesNotThrowException()
    {
        var dosageFormName = Fake.DosageForm.Name;

        var request = new CreateDosageFormCommandRequest(dosageFormName);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_NameExists_DoesNotThrowException()
    {
        var dosageFormName = Fake.DosageForm.Name;

        dosageFormRepository.ExistsAsync(Arg.Any<Expression<Func<DosageForm, bool>>>()).Returns(true);

        var request = new CreateDosageFormCommandRequest(dosageFormName);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains($"Dosage form with name '{dosageFormName}' already exists", result.Errors[0].ErrorMessage);
    }
}
