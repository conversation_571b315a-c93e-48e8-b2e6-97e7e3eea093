﻿using Axon.HAComms.Application.Commands.DosageForms.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DosageForms.Update;

public class UpdateDosageFormValidatorTests
{
    private readonly UpdateDosageFormCommandValidator sut;

    public UpdateDosageFormValidatorTests()
    {
        var dosageFormRepository = Substitute.For<IDosageFormsRepository>();
        sut = new UpdateDosageFormCommandValidator(dosageFormRepository);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateDosageFormCommandRequest(0, Fake.DosageForm.Name));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateDosageFormCommandRequest(Fake.DosageForm.Id, Fake.DosageForm.Name));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_NameIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateDosageFormCommandRequest(Fake.DosageForm.Id, string.Empty));
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);

        var result = sut.TestValidate(new UpdateDosageFormCommandRequest(Fake.DosageForm.Id, longName));
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);

        var result = sut.TestValidate(new UpdateDosageFormCommandRequest(Fake.DosageForm.Id, validName));
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }
}
