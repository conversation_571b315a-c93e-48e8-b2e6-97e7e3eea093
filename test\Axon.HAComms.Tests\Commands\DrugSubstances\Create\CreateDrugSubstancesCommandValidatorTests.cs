﻿using Axon.HAComms.Application.Commands.DrugSubstances.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Create;

public class CreateDrugSubstancesCommandValidatorTests
{
    private readonly CreateDrugSubstanceCommandValidator sut;

    public CreateDrugSubstancesCommandValidatorTests()
    {
        var repoDrugSubstances = Substitute.For<IDrugSubstancesRepository>();
        sut = new CreateDrugSubstanceCommandValidator(repoDrugSubstances);
    }

    [Fact]
    public void Validate_SubstanceCodeIsEmpty_ThrowsException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, string.Empty, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Code);
        Assert.Contains("'Code' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_SubstanceCodeIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceNameIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, string.Empty);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }
}
