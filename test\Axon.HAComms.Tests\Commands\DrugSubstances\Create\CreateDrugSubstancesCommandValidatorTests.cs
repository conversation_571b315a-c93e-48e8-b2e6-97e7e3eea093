﻿using Axon.HAComms.Application.Commands.DrugSubstances.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Create;

public class CreateDrugSubstancesCommandValidatorTests
{
    private readonly CreateDrugSubstanceCommandValidator sut;

    public CreateDrugSubstancesCommandValidatorTests()
    {
        var repoDrugSubstances = Substitute.For<IDrugSubstancesRepository>();
        sut = new CreateDrugSubstanceCommandValidator(repoDrugSubstances);
    }

    [Fact]
    public void Validate_SubstanceCodeIsEmpty_ThrowsException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, string.Empty, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Code);
        Assert.Contains("'Code' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_SubstanceCodeIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceNameIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, string.Empty);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_CodeExceedsMaxLength_ThrowsException()
    {
        var longCode = Fake.GetRandomString(51);
        var drugSubstanceDescription = Fake.DrugSubstance.Description;
        var drugSubstanceName = Fake.DrugSubstance.Name;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, longCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Code);
        Assert.Contains("Code cannot exceed 50 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_CodeDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validCode = Fake.GetRandomString(50);
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, validCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(longName, drugSubstanceCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100); // Exactly 100 characters
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(validName, drugSubstanceCode, drugSubstanceDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionExceedsMaxLength_ThrowsException()
    {
        var longDescription = Fake.GetRandomString(501); // Exceeds 500 character limit
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, longDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Description);
        Assert.Contains("Description cannot exceed 500 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DescriptionDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validDescription = Fake.GetRandomString(500); // Exactly 500 characters
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, validDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_DescriptionIsNull_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, null);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }
}
