﻿using Axon.HAComms.Application.Commands.DrugSubstances.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Update;

public class UpdateDrugSubstancesValidatorTests
{
    private readonly UpdateDrugSubstanceCommandValidator sut;

    public UpdateDrugSubstancesValidatorTests()
    {
        var repoDrugSubstances = Substitute.For<IDrugSubstancesRepository>();
        sut = new UpdateDrugSubstanceCommandValidator(repoDrugSubstances);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, drugSubstanceName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_SubstanceCodeIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, string.Empty, string.Empty, string.Empty));
        result.ShouldHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceCodeIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, string.Empty, drugSubstanceCode, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceNameIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceName = Fake.DrugSubstance.Name;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, drugSubstanceName, drugSubstanceCode, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_CodeExceedsMaxLength_ThrowsException()
    {
        var longCode = Fake.GetRandomString(51);
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, longCode, drugSubstanceDescription));
        result.ShouldHaveValidationErrorFor(x => x.Code);
        Assert.Contains("Code cannot exceed 50 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_CodeDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validCode = Fake.GetRandomString(50);
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, validCode, drugSubstanceDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, longName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, validName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionExceedsMaxLength_ThrowsException()
    {
        var longDescription = Fake.GetRandomString(501);
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, drugSubstanceCode, longDescription));
        result.ShouldHaveValidationErrorFor(x => x.Description);
        Assert.Contains("Description cannot exceed 500 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DescriptionDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validDescription = Fake.GetRandomString(500);
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, drugSubstanceCode, validDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_DescriptionIsNull_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, drugSubstanceCode, null));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }
}
