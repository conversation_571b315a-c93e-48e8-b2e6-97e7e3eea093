﻿using Axon.HAComms.Application.Commands.DrugSubstances.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Update;

public class UpdateDrugSubstancesValidatorTests
{
    private readonly UpdateDrugSubstanceCommandValidator sut;

    public UpdateDrugSubstancesValidatorTests()
    {
        var repoDrugSubstances = Substitute.For<IDrugSubstancesRepository>();
        sut = new UpdateDrugSubstanceCommandValidator(repoDrugSubstances);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, drugSubstanceName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(Fake.DrugSubstance.Id, drugSubstanceName, drugSubstanceCode, drugSubstanceDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_SubstanceCodeIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, string.Empty, string.Empty, string.Empty));
        result.ShouldHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceCodeIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceCode = Fake.DrugSubstance.Code;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, string.Empty, drugSubstanceCode, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_SubstanceNameIsNotEmpty_DoesNotThrowException()
    {
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceName = Fake.DrugSubstance.Name;

        var result = sut.TestValidate(new UpdateDrugSubstanceCommandRequest(0, drugSubstanceName, drugSubstanceCode, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }
}
