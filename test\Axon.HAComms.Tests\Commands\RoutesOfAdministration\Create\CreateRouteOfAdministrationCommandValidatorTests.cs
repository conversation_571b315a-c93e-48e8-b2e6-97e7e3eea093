﻿using Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Create;

public class CreateRouteOfAdministrationCommandValidatorTests
{
    private readonly CreateRouteOfAdministrationCommandValidator sut;

    public CreateRouteOfAdministrationCommandValidatorTests()
    {
        var routeOfAdministrationRepository = Substitute.For<IRouteOfAdministrationRepository>();
        sut = new CreateRouteOfAdministrationCommandValidator(routeOfAdministrationRepository);
    }

    [Fact]
    public void Validate_NameIsEmpty_DoesThrowException()
    {
        var request = new CreateRouteOfAdministrationCommandRequest(string.Empty);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("'Name' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameIsNotEmpty_DoesNotThrowException()
    {
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;

        var request = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);

        var request = new CreateRouteOfAdministrationCommandRequest(longName);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);

        var request = new CreateRouteOfAdministrationCommandRequest(validName);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }
}
