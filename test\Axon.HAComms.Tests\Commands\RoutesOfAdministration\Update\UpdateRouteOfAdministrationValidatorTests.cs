﻿using Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Update;

public class UpdateRouteOfAdministrationValidatorTests
{
    private readonly UpdateRouteOfAdministrationCommandValidator sut;

    public UpdateRouteOfAdministrationValidatorTests()
    {
        var routeOfAdministrationRepository = Substitute.For<IRouteOfAdministrationRepository>();
        sut = new UpdateRouteOfAdministrationCommandValidator(routeOfAdministrationRepository);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(0, Fake.RouteOfAdministration.Name));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, Fake.RouteOfAdministration.Name));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_NameIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, string.Empty));
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }
}
