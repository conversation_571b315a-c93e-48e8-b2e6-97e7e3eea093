﻿using Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Update;

public class UpdateRouteOfAdministrationValidatorTests
{
    private readonly UpdateRouteOfAdministrationCommandValidator sut;

    public UpdateRouteOfAdministrationValidatorTests()
    {
        var routeOfAdministrationRepository = Substitute.For<IRouteOfAdministrationRepository>();
        sut = new UpdateRouteOfAdministrationCommandValidator(routeOfAdministrationRepository);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(0, Fake.RouteOfAdministration.Name));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, Fake.RouteOfAdministration.Name));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_NameIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, string.Empty));
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);

        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, longName));
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);

        var result = sut.TestValidate(new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, validName));
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }
}
