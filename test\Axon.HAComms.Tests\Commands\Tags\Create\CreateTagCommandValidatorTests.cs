﻿using Axon.HAComms.Application.Commands.Tags.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Create;

public class CreateTagCommandValidatorTests
{
    private readonly CreateTagCommandValidator sut;

    public CreateTagCommandValidatorTests()
    {
        var tagsRepository = Substitute.For<ITagRepository>();
        sut = new CreateTagCommandValidator(tagsRepository);
    }

    [Fact]
    public void Validate_NameIsEmpty_DoesThrowException()
    {
        var request = new CreateTagCommandRequest(string.Empty, Fake.Tag.Description);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("'Name' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameIsNotEmpty_DoesNotThrowException()
    {
        var tagName = Fake.Tag.Name;

        var request = new CreateTagCommandRequest(tagName, Fake.Tag.Description);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionIsEmpty_DoesNotThrowException()
    {
        var tagName = Fake.Tag.Name;

        var request = new CreateTagCommandRequest(tagName, string.Empty);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(101);
        var tagDescription = Fake.Tag.Description;

        var request = new CreateTagCommandRequest(longName, tagDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 100 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);
        var tagDescription = Fake.Tag.Description;

        var request = new CreateTagCommandRequest(validName, tagDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionExceedsMaxLength_ThrowsException()
    {
        var longDescription = Fake.GetRandomString(501);
        var tagName = Fake.Tag.Name;

        var request = new CreateTagCommandRequest(tagName, longDescription);
        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Description);
        Assert.Contains("Description cannot exceed 500 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DescriptionDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validDescription = Fake.GetRandomString(500);
        var tagName = Fake.Tag.Name;

        var request = new CreateTagCommandRequest(tagName, validDescription);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_DescriptionIsNull_DoesNotThrowException()
    {
        var tagName = Fake.Tag.Name;

        var request = new CreateTagCommandRequest(tagName, null);
        var result = sut.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }
}
