﻿using Axon.HAComms.Application.Commands.Tags.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Update;

public class UpdateTagValidatorTests
{
    private readonly UpdateTagCommandValidator sut;

    public UpdateTagValidatorTests()
    {
        var tagsRepository = Substitute.For<ITagRepository>();
        sut = new UpdateTagCommandValidator(tagsRepository);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(0, Fake.Tag.Name, Fake.Tag.Description));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, Fake.Tag.Name, Fake.Tag.Description));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_NameIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, string.Empty, Fake.Tag.Description));
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionIsEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, Fake.Tag.Name, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }
}
